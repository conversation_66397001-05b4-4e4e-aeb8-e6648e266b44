/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     tp_simple_player_frame_callback.h
 * @brief    数据帧回调示例
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/09/19
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#pragma once

#include "ThumbPlayer/api/ThumbPlayer.h"
#include <d3d9.h>

class TPSimplePlayerFrameCallback : public CDialogEx,
                                    public ITPPlayerCallback ::IOnPreparedCallback,
                                    public ITPPlayerCallback ::IOnVideoFrameOutCallback,
                                    public ITPPlayerCallback ::IOnAudioFrameOutCallback {
  DECLARE_DYNAMIC(TPSimplePlayerFrameCallback)
  DECLARE_MESSAGE_MAP()

 public:
  explicit TPSimplePlayerFrameCallback(CWnd *pParent = nullptr);  // 标准构造函数
  virtual ~TPSimplePlayerFrameCallback();
  BOOL OnInitDialog() override;
 
 protected:
  virtual void DoDataExchange(CDataExchange *pDX);
  afx_msg void OnClose();
  afx_msg void OnClickedVideoCb();
  afx_msg void OnClickedAudioCb();
  void CreatePlayerAndPlay();
  void OnPrepared(ITPPlayer *player) override;
  void DestroyPlayer();
  void SetDataSource();
  void OnVideoFrameOut(ITPPlayer *player, ITPVideoFrameBuffer *video_frame_buffer) override;
  void OnAudioFrameOut(ITPPlayer *player, ITPAudioFrameBuffer *audio_frame_buffer) override;


 public:
  CStatic preview_window_;
  CButton check_video_frame_callback_;
  CButton check_audio_frame_callback_;
  CStatic text_video_frame_count_;
  CStatic text_audio_frame_count_;

 private:
  // 播放器实例
  ITPPlayer *player_ = nullptr;
  int video_frame_count_ = 0;
  int audio_frame_count_ = 0;

  // D3D9 渲染相关成员
  IDirect3D9* d3d9_ = nullptr;
  IDirect3DDevice9* d3d_device_ = nullptr;
  IDirect3DTexture9* video_texture_ = nullptr;
  int texture_width_ = 0;
  int texture_height_ = 0;

  // 渲染相关方法
  bool InitD3D9();
  void CleanupD3D9();
  bool CreateVideoTexture(int width, int height);
  bool RenderGDIFrame(int width, int height, HANDLE handle);
  bool RenderD3D9Frame(int width, int height, HANDLE handle);
  bool UpdateTextureFromMemory(int width, int height, void* data);
  void RenderTextureToWindow();
};
