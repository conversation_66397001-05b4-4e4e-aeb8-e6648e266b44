#pragma once

#include <string>
#include <mutex>
#include <vector>
#include "ITPPlayer.h"

#include <d3d9.h>
#include <d3d11.h>

#include "ThumbPlayer/api/ThumbPlayer.h"
#include "IUnityInterface.h"
#include "IUnityGraphics.h"
#include "IUnityGraphicsD3D9.h"
#include "IUnityGraphicsD3D11.h"

namespace TP {

    typedef struct SharedSurfaceHandleInfo {
        int width = 0;
        int height = 0;
        HANDLE handle = nullptr;
        bool isRendered = false;

        void updateFrame(int newWidth, int newHeight, uint8_t* newHandle = nullptr) {
            width = newWidth;
            height = newHeight;
            handle = newHandle;
            isRendered = false;
        }
    } SharedSurfaceHandleInfo;

    class TPPlayer : public ITPPlayer,
					 public ITPPlayerCallback::IOnPreparedCallback,
			         public ITPPlayerCallback::IOnCompletionCallback,
			         public ITPPlayerCallback::IOnInfoCallback,
			         public ITPPlayerCallback::IOnErrorCallback,
					 public ITPPlayerCallback::IOnVideoFrameOutCallback
	{
    public:
        const int STATE_NONE = 0;
        const int STATE_PREPARED = 1;
        const int STATE_READY = 2;

        TPPlayer(IUnityInterfaces* interfaces, bool forceGdiMode = false);

        virtual ~TPPlayer();

        void setDataSource(const std::string& source, int64_t startTime) override;

        void switchDataSource(const std::string& source, int mode) override;

        void prepare() override;

        void play() override;

        void resume() override;

        void pause() override;

        void stop() override;

        void destroy() override;

        void seek(double sec) override;

        void setVolume(int volume) override;

        void setMute(bool mute) override;

        void setLoop(bool loop) override;

        void setPlaySpeed(float speed_ratio) override;

        double position() override;

        double duration() override;

        int width() override;

        int height() override;

        double bufferPercentage() override;

        int pixelFormat() override;

        void* getTexturePointer(int index) override;

        void onPlayEvent(TPEventType event, const char* detail) override;

        void updateFrame(ITPVideoFrameBuffer* frame);

        // run on render thread
        void render() override;

        void clear() override;

        void deviceBeforeReset() override;
        void deviceAfterReset() override;

        void clearTextures();
        bool uploadTextures(SharedSurfaceHandleInfo info);
        bool uploadTexturesD3D9(SharedSurfaceHandleInfo info);
        bool uploadTexturesD3D11(SharedSurfaceHandleInfo info);
        bool uploadTexturesD3D11WithGdiData(SharedSurfaceHandleInfo info);

        void OnPrepared(::ITPPlayer* player) override;
        void OnCompletion(::ITPPlayer* player) override;
        void OnInfo(::ITPPlayer* player, TPOnInfoID on_info_id, TPOnInfoParam on_info_param) override;
        void OnError(::ITPPlayer* player, ITPError* error) override;
        void OnVideoFrameOut(::ITPPlayer* player, ITPVideoFrameBuffer* video_frame_buffer) override;

    private:
        ::ITPPlayer* player = nullptr;

        int state;
        std::string url;
        std::mutex mutex;

        SharedSurfaceHandleInfo info;

        IUnityInterfaces* interfaces;

        // D3D9 members
        IDirect3DDevice9* m_d3d9Device;
        IDirect3DTexture9* m_d3d9Texture;
        std::vector<IDirect3DTexture9*> m_d3d9TexturesToDelete;  // Queue of textures waiting for deletion

        // D3D9 synchronization for safe texture deletion
        bool m_d3d9ReadyEventSent;
        bool m_d3d9TextureConsumed;

        // Shared memory data cache (used by both D3D9 and D3D11 GDI modes)
        uint8_t* m_sharedMemoryCachedData;
        int m_cachedDataSize;

        // D3D11 members
        ID3D11Device* m_d3d11Device;
        ID3D11DeviceContext* m_d3d11DeviceContext = nullptr;
        ID3D11Texture2D* m_d3d11Texture;

        int m_textureWidth;
        int m_textureHeight;

        // Renderer type tracking
        bool m_isD3D9Mode;
        bool m_forceGdiMode;
    };

    // Factory functions
    ITPPlayer* createWindowsPlayer(void* interfaces, bool forceGdiMode = false);
}