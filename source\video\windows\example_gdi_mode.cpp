// Example: How to use GDI mode in D3D11 environment
// This example demonstrates how to create a player with forced GDI mode

#include "TPPlayer.h"

void ExampleCreatePlayerWithGdiMode() {
    // Get Unity interfaces (this would be provided by Unity)
    IUnityInterfaces* interfaces = nullptr; // In real usage, this comes from Unity

    // Method 1: Create player with default mode (auto-detect D3D9/D3D11)
    TP::ITPPlayer* defaultPlayer = TP::createWindowsPlayer(interfaces);

    // Method 2: Create player with forced GDI mode (D3D11 device + shared memory data)
    TP::ITPPlayer* gdiPlayer = TP::createWindowsPlayerWithGdiMode(interfaces, true);

    // Method 3: Create player with explicit GDI mode control
    bool useGdiMode = true; // Set this based on your requirements
    TP::ITPPlayer* controlledPlayer = TP::createWindowsPlayerWithGdiMode(interfaces, useGdiMode);

    // The GDI mode player will:
    // 1. Use kTPVideoRendererTypeGdi renderer type
    // 2. Use kTPPixelFormatSharedMemory pixel format
    // 3. Use D3D11 device but process shared memory data (not DXGI shared surface)
    // 4. Create D3D11 texture with CPU write access for memory data upload
    
    // Clean up
    if (defaultPlayer) {
        defaultPlayer->destroy();
        delete defaultPlayer;
    }
    
    if (gdiPlayer) {
        gdiPlayer->destroy();
        delete gdiPlayer;
    }
    
    if (controlledPlayer) {
        controlledPlayer->destroy();
        delete controlledPlayer;
    }
}

// Example usage in Unity plugin context
extern "C" {
    // Export function for Unity to create player with GDI mode
    __declspec(dllexport) TP::ITPPlayer* CreatePlayerWithGdiMode(void* interfaces, bool forceGdi) {
        return TP::createWindowsPlayerWithGdiMode(interfaces, forceGdi);
    }
    
    // Export function for Unity to create default player
    __declspec(dllexport) TP::ITPPlayer* CreatePlayer(void* interfaces) {
        return TP::createWindowsPlayer(interfaces);
    }
}
