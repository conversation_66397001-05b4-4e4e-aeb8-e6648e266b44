#pragma once

#include "TPPlayer.h"

#include <sstream>
#include <vector>
#include <cinttypes>

#define TAG "TPPlayer"
#define EVENT_OK "ok"

namespace TP
{
	class TPLogDelegate;
}

TP::TPPlayer::TPPlayer(IUnityInterfaces* interfaces, bool forceGdiMode) {
	this->interfaces = interfaces;
	this->playerId = newId();
	this->state = STATE_NONE;
	this->eventHandler = nullptr;
	this->info.updateFrame(0, 0, nullptr);
	this->m_forceGdiMode = forceGdiMode;

	this->player = TPCreatePlayer();

	this->player->SetOnPreparedCallback(this);
	this->player->SetOnCompletionCallback(this);
	this->player->SetOnInfoCallback(this);
	this->player->SetOnErrorCallback(this);
	this->player->SetOnVideoFrameOutCallback(this);

	// Initialize D3D9 members
	m_d3d9Device  = nullptr;
	m_d3d9Texture = nullptr;
	m_d3d9TextureToDelete = nullptr;

	// Initialize shared memory cache
	m_sharedMemoryCachedData = nullptr;
	m_cachedDataSize = 0;


	// Initialize D3D11 members
	m_d3d11Device = nullptr;
	m_d3d11DeviceContext = nullptr;
	m_d3d11Texture = nullptr;

	// 支持D3D9和D3D11
	{
		m_isD3D9Mode = false;

		if (m_d3d11Device == nullptr && m_d3d9Device == nullptr && interfaces != nullptr)
		{
			auto initializeGraphicsDevice = [&]() {
				IUnityGraphicsD3D9* d3d9 = interfaces->Get<IUnityGraphicsD3D9>();
				if (d3d9 && (m_d3d9Device = d3d9->GetDevice()) != nullptr)
				{
					HRESULT hr = m_d3d9Device->TestCooperativeLevel();

					if (SUCCEEDED(hr))
					{
						m_isD3D9Mode = true;
						return;
					}
					m_d3d9Device = nullptr;
				}

				// 尝试D3D11
				IUnityGraphicsD3D11* d3d11 = interfaces->Get<IUnityGraphicsD3D11>();

				if (d3d11 && (m_d3d11Device = d3d11->GetDevice()) != nullptr)
				{
					m_isD3D9Mode = false;
				}
			};

			initializeGraphicsDevice();
		}

		ITPArray<int>* render_array = CreateIntTPArray();
        // 根据参数决定渲染器类型
		if (m_forceGdiMode || m_isD3D9Mode)
		{
			render_array->AddValue(kTPVideoRendererTypeGdi);
		}
		else
		{
			render_array->AddValue(kTPVideoRendererTypeD3d11);
		}
		ITPOptionalParam<ITPArray<int>*>* param = BuildQueueIntParam(kTPOptionalIDBeforeQueueIntVideoRendererType, render_array);
		this->player->AddOptionalParam(param);
		param->Destroy();
		render_array->Destroy();
	}

	{
		auto param = BuildBoolParam(kTPOptionalIDGlobalBoolEnableVideoFrameCallback, true);
		this->player->AddOptionalParam(param);
		param->Destroy();

		auto param_content = BuildIntParam(kTPOptionalIDBeforeIntOutVideoContentMode, kTPVideoCallbackContentModeAll);
		this->player->AddOptionalParam(param_content);
		param_content->Destroy();

		// 根据渲染器类型设置不同的像素格式
		if (m_forceGdiMode || m_isD3D9Mode)
		{
			auto param_format = BuildIntParam(kTPOptionalIDBeforeIntOutVideoPixelFormat, kTPPixelFormatSharedMemory);
			this->player->AddOptionalParam(param_format);
			param_format->Destroy();
		}
		else
		{
			auto param_format = BuildIntParam(kTPOptionalIDBeforeIntOutVideoPixelFormat, kTPPixelFormatDxgiSharedSurface);
			this->player->AddOptionalParam(param_format);
			param_format->Destroy();
		}
	}

	m_textureWidth = 0;
	m_textureHeight = 0;

	RECT rect;
	ZeroMemory(&rect, sizeof(rect));
	rect.right = 1920;
	rect.bottom = 1080;
	this->player->GetPresenterInterface()->NotifyWindowsResize(rect);
}

TP::TPPlayer::~TPPlayer()
{
	{
		std::lock_guard<std::mutex> lock(this->mutex);
		// Clean up D3D11 resources
		if (m_d3d11DeviceContext)
		{
			m_d3d11DeviceContext->Release();
			m_d3d11DeviceContext = nullptr;
		}

		// Clean up cached data
		if (m_sharedMemoryCachedData)
		{
			delete[] m_sharedMemoryCachedData;
			m_sharedMemoryCachedData = nullptr;
			m_cachedDataSize = 0;
		}
	}

	destroy();
	clearTextures();
}

void TP::TPPlayer::setDataSource(const std::string& source, int64_t startTime) {
	this->url = source;
	this->state = STATE_NONE;
	if (!this->url.empty()) {
		ITPUrlMediaAsset* asset = TPCreateUrlMediaAsset();
		asset->SetUrl(source.c_str());
		if (startTime > 0)
		{
			auto param_playingTime = BuildLongParam(kTPOptionalIDBeforeLongStartPlayingTimeMs, startTime);
			this->player->AddOptionalParam(param_playingTime);
			param_playingTime->Destroy();


			auto param_accurateSeek = BuildBoolParam(kTPOptionalIDBeforeBoolStartPlayingTimeAccurateSeek, true);
			this->player->AddOptionalParam(param_accurateSeek);
			param_accurateSeek->Destroy();
		}
		this->player->SetDataSource(asset);
		asset->Destroy();
	}
}

void TP::TPPlayer::switchDataSource(const std::string& source, int mode) {
	this->url = source;
	if (!this->url.empty()) {		
		ITPUrlMediaAsset* asset = TPCreateUrlMediaAsset();
		asset->SetUrl(source.c_str());
		this->player->SwitchDataSourceAsync(asset, (::TPSwitchDataSourceMode)mode);
		asset->Destroy();
	}
}

void TP::TPPlayer::prepare() {
	if (!this->url.empty()) {
		this->player->PrepareAsync();
	}
}

void TP::TPPlayer::play() {
	if (!this->url.empty()) {
		this->player->Start();
	}
}

void TP::TPPlayer::resume() {
	if (!this->url.empty()) {
		this->player->Start();
	}
}

void TP::TPPlayer::pause() {
	if (!this->url.empty()) {
		this->player->Pause();
	}
}

void TP::TPPlayer::stop() {
	if (!this->url.empty()) {
		this->player->Stop();
	}
}

void TP::TPPlayer::destroy() {
	if (nullptr == this->player) {
		return;
	}
	this->player->SetOnPreparedCallback(nullptr);
	this->player->SetOnCompletionCallback(nullptr);
	this->player->SetOnInfoCallback(nullptr);
	this->player->SetOnErrorCallback(nullptr);
	this->player->SetOnVideoFrameOutCallback(nullptr);

	this->player->Stop();
	this->player->Reset();
	this->player->Release();
	this->player->Destroy();
	this->player = nullptr;
}

void TP::TPPlayer::seek(double sec) {
	if (!this->url.empty()) {
		this->player->SeekToAsync((int64_t)(sec * 1000), kTPSeekModeAccuratePosition);
	}
}

void TP::TPPlayer::setVolume(int volume) {
	this->player->SetAudioVolume(volume / 100.0f);
}


void TP::TPPlayer::setMute(bool mute) {
	this->player->SetAudioMute(mute);
}

void TP::TPPlayer::setPlaySpeed(float speed_ratio) {
	this->player->SetPlaySpeedRatio(speed_ratio);
}

void TP::TPPlayer::setLoop(bool loop) {
	this->player->SetLoopback(loop);
}

double TP::TPPlayer::position() {
	return this->player->GetCurrentPositionMs() / 1000.0;
}

double TP::TPPlayer::duration() {
	return this->player->GetDurationMs() / 1000.0;
}

int TP::TPPlayer::width() {
	return (int)this->player->GetWidth();
}

int TP::TPPlayer::height() {
	return (int)this->player->GetHeight();
}

double TP::TPPlayer::bufferPercentage() {
	return 1;
}

// 当前只支持DXD11
int TP::TPPlayer::pixelFormat() {
	// https://iwiki.woa.com/p/4006804502
	// 当前HDR模式使用的是R10G10B10A2，SDR模式使用的是B8G8R8A8
	return kTPPixelFormatBGR24;
}

void* TP::TPPlayer::getTexturePointer(int index)
{
    switch (index)
    {
    case 0:
        if (m_isD3D9Mode)
        {
            return m_d3d9Texture;
        }
        else
        {
            // Both D3D11 native mode and D3D11 with GDI mode use D3D11 texture
            return m_d3d11Texture;
        }
    default: return nullptr;
    }
}

void TP::TPPlayer::onPlayEvent(TPEventType event, const char* detail) {
	int idEvent = VP_ID_EVENT(this->playerId, (int) event);
	if (eventHandler != nullptr) {
		eventHandler(idEvent, detail);
	}
}

void TP::TPPlayer::updateFrame(ITPVideoFrameBuffer* frame) {
	if (this->state == STATE_NONE) {
		this->state = STATE_PREPARED;
	}
	const int videoWidth = this->player->GetWidth();
	const int videoHeight = this->player->GetHeight();
	const int frameWidth = frame->GetWidth();
	const int frameHeight = frame->GetHeight();
	{
		std::lock_guard<std::mutex> lock(this->mutex);
		if (m_forceGdiMode || m_isD3D9Mode) {
			// GDI模式（包括D3D9和强制GDI模式）下缓存共享内存数据
			wchar_t* originalName = reinterpret_cast<wchar_t*>(frame->GetData()[0]);
			if (originalName) {
				int len = frameWidth * frameHeight * 4;  // BGRA 格式，每像素 4 字节

				// 打开原始共享内存
				HANDLE file_handle = ::CreateFileMapping(INVALID_HANDLE_VALUE, nullptr, PAGE_READWRITE, 0, len, originalName);
				if (file_handle != INVALID_HANDLE_VALUE) {
					void* originalData = ::MapViewOfFile(file_handle, FILE_MAP_READ, 0, 0, len);
					if (originalData) {
						// 重新分配缓存内存（如果尺寸变化）
						if (!m_sharedMemoryCachedData || m_cachedDataSize != len) {
							if (m_sharedMemoryCachedData) {
								delete[] m_sharedMemoryCachedData;
							}
							m_sharedMemoryCachedData = new uint8_t[len];
							m_cachedDataSize = len;
						}

						// 复制数据到缓存
						memcpy(m_sharedMemoryCachedData, originalData, len);

						// 清理原始共享内存
						::UnmapViewOfFile(originalData);
					}
					CloseHandle(file_handle);
				}
			}

			// 使用缓存的数据指针更新info
			this->info.updateFrame(frameWidth, frameHeight, m_sharedMemoryCachedData);
		} else {
			// D3D11原生模式保持原有逻辑
			this->info.updateFrame(frameWidth, frameHeight, frame->GetData()[0]);
		}
	}
	if (videoWidth != frameWidth || videoHeight != frameHeight)
	{
		RECT rect;
		ZeroMemory(&rect, sizeof(rect));
		rect.right = videoWidth;
		rect.bottom = videoHeight;
		this->player->GetPresenterInterface()->NotifyWindowsResize(rect);
	}
}

void TP::TPPlayer::render() {
	if (m_d3d11Device == nullptr && m_d3d9Device == nullptr && interfaces != nullptr)
	{
		if (m_isD3D9Mode)
		{
			IUnityGraphicsD3D9* d3d9 = interfaces->Get<IUnityGraphicsD3D9>();
			if (d3d9 != nullptr)
			{
				m_d3d9Device = d3d9->GetDevice();
			}
		}
		else
		{
			IUnityGraphicsD3D11* d3d11 = interfaces->Get<IUnityGraphicsD3D11>();
			if (d3d11 != nullptr)
			{
				m_d3d11Device = d3d11->GetDevice();
			}
		}
	}

    // Check if we have a valid device
    if (m_d3d11Device == nullptr && m_d3d9Device == nullptr)
    {
        return;
    }

	{
		std::lock_guard<std::mutex> lock(this->mutex);
		if (!this->info.isRendered) {
			uploadTextures(this->info);
			this->info.isRendered = true;
		}
	}
	if (this->state == STATE_PREPARED) {
		this->state = STATE_READY;
		this->onPlayEvent(TPEventType::READY, EVENT_OK);
	}
}

void TP::TPPlayer::clear() {
	clearTextures();
}

void TP::TPPlayer::clearTextures() {
	std::lock_guard<std::mutex> lock(this->mutex);
    // Clear D3D11 texture
    if (m_d3d11Texture)
    {
        m_d3d11Texture->Release();
        m_d3d11Texture = nullptr;
    }

    // Clear D3D9 textures
    if (m_d3d9Texture)
    {
        m_d3d9Texture->Release();
        m_d3d9Texture = nullptr;
    }
    if (m_d3d9TextureToDelete)
    {
        m_d3d9TextureToDelete->Release();
        m_d3d9TextureToDelete = nullptr;
    }
}

void TP::TPPlayer::deviceBeforeReset()
{
	clearTextures();
	onPlayEvent(ITPEventHandler::TPEventType::DEVICE_RESET_START, EVENT_OK);
}

void TP::TPPlayer::deviceAfterReset()
{
	if (m_isD3D9Mode)
	{
		std::lock_guard<std::mutex> lock(this->mutex);
		uploadTexturesD3D9(this->info);
	}
	onPlayEvent(ITPEventHandler::TPEventType::DEVICE_RESET_END, EVENT_OK);
}

bool TP::TPPlayer::uploadTextures(SharedSurfaceHandleInfo info) {

    // Check if we have a valid device
    if (m_d3d11Device == nullptr && m_d3d9Device == nullptr)
    {
        return false;
    }

    if (m_isD3D9Mode)
    {
        // Pure D3D9 implementation - use D3D9 device with shared memory
        return uploadTexturesD3D9(info);
    }
    else if (m_forceGdiMode)
    {
        // D3D11 device with GDI mode - use D3D11 device with shared memory data
        return uploadTexturesD3D11WithGdiData(info);
    }
    else
    {
        // D3D11 implementation - use DXGI shared surface
        return uploadTexturesD3D11(info);
    }
}

bool TP::TPPlayer::uploadTexturesD3D11(SharedSurfaceHandleInfo info) {
    if (!m_d3d11Device) {
		return false;
	}

	bool renderTextureUpdated = false;

	// Create or update the RGBA texture.
    if (!m_d3d11Texture || m_textureWidth != info.width || m_textureHeight != info.height) {
		m_textureWidth = info.width;
		m_textureHeight = info.height;		

		D3D11_TEXTURE2D_DESC desc;
		ZeroMemory(&desc, sizeof(desc));
		desc.Width = m_textureWidth;
		desc.Height = m_textureHeight;
		desc.MipLevels = 1;
		desc.ArraySize = 1;
		desc.Format = DXGI_FORMAT_B8G8R8A8_UNORM;
		desc.SampleDesc.Count = 1;
		desc.Usage = D3D11_USAGE_DEFAULT;
		desc.BindFlags = D3D11_BIND_SHADER_RESOURCE | D3D11_BIND_RENDER_TARGET;

        if (m_d3d11Texture) {
            m_d3d11Texture->Release();
            m_d3d11Texture = nullptr;
		}

		renderTextureUpdated = true;
		HRESULT hr = m_d3d11Device->CreateTexture2D(&desc, nullptr, &m_d3d11Texture);
		if (FAILED(hr)) {
			return false;
		}
	}

	ID3D11Texture2D* sharedTexture = nullptr;
	HANDLE handle = reinterpret_cast<HANDLE>(info.handle);
    HRESULT hr = m_d3d11Device->OpenSharedResource(handle, __uuidof(ID3D11Texture2D), reinterpret_cast<LPVOID*>(&sharedTexture));
	if (FAILED(hr) || sharedTexture == nullptr) {
		return false;
	}

	IDXGIKeyedMutex* mutex = nullptr;
	UINT acqKey = 1;
	UINT relKey = 0;
	DWORD timeOut = 5;
	hr = sharedTexture->QueryInterface(__uuidof(IDXGIKeyedMutex), reinterpret_cast<LPVOID*>(&mutex));
	if (FAILED(hr) || mutex == nullptr) {
		if (sharedTexture) {
			sharedTexture->Release();
			sharedTexture = nullptr;
		}
		return false;
	}

	DWORD result = mutex->AcquireSync(acqKey, timeOut);
	if (result == WAIT_OBJECT_0) {
        if (m_d3d11DeviceContext == nullptr) {
            m_d3d11Device->GetImmediateContext(&m_d3d11DeviceContext);
		}

        m_d3d11DeviceContext->CopyResource(m_d3d11Texture, sharedTexture);

		hr = mutex->ReleaseSync(relKey);
		if (FAILED(hr)) {
			// error
		}
	}

	if (sharedTexture) {
		sharedTexture->Release();
		sharedTexture = nullptr;
	}
	if (mutex) {
		mutex->Release();
	}

	if (renderTextureUpdated) {
		onPlayEvent(ITPEventHandler::TPEventType::READY, EVENT_OK);
	}
	return true;
}

bool TP::TPPlayer::uploadTexturesD3D9(SharedSurfaceHandleInfo info)
{
    // 参数验证
    if (!m_d3d9Device || !info.handle || info.width <= 0 || info.height <= 0)
    {
        return false;
    }

    bool renderTextureUpdated = false;

    // 延迟删除上一帧标记的纹理
    if (m_d3d9TextureToDelete)
    {
        m_d3d9TextureToDelete->Release();
        m_d3d9TextureToDelete = nullptr;
    }

    // 检查是否需要创建新纹理
    const bool needNewTexture = !m_d3d9Texture ||
                                m_textureWidth != info.width ||
                                m_textureHeight != info.height;

    if (needNewTexture)
    {
        if (m_d3d9Texture)
        {
            m_d3d9TextureToDelete = m_d3d9Texture;
            m_d3d9Texture = nullptr;
        }

        m_textureWidth = info.width;
        m_textureHeight = info.height;
        renderTextureUpdated = true;

        HRESULT hr = m_d3d9Device->CreateTexture(
            m_textureWidth,
            m_textureHeight,
            1,
            D3DUSAGE_RENDERTARGET,
            D3DFMT_A8R8G8B8,
            D3DPOOL_DEFAULT,
            &m_d3d9Texture,
            nullptr
        );

        if (FAILED(hr) || !m_d3d9Texture)
        {
            return false;
        }
    }

    const uint8_t* srcData = static_cast<const uint8_t*>(info.handle);
    if (!srcData)
    {
        return false;
    }

    struct D3D9ResourceGuard {
        IDirect3DTexture9* texture = nullptr;
        bool isLocked = false;

        ~D3D9ResourceGuard() {
            if (isLocked && texture) {
                texture->UnlockRect(0);
            }
            if (texture) {
                texture->Release();
            }
        }
    } guard;

    HRESULT hr = m_d3d9Device->CreateTexture(
        m_textureWidth,
        m_textureHeight,
        1,
        0,
        D3DFMT_A8R8G8B8,
        D3DPOOL_SYSTEMMEM,
        &guard.texture,
        nullptr
    );

    if (FAILED(hr) || !guard.texture)
    {
        return false;
    }

    D3DLOCKED_RECT lockedRect;
    hr = guard.texture->LockRect(0, &lockedRect, nullptr, 0);
    if (FAILED(hr))
    {
        return false;
    }
    guard.isLocked = true;

    const int srcPitch = m_textureWidth * 4;  // BGRA格式，每像素4字节
    uint8_t* dstData = static_cast<uint8_t*>(lockedRect.pBits);

    if (lockedRect.Pitch == srcPitch)
    {
        const int totalBytes = srcPitch * m_textureHeight;
        memcpy(dstData, srcData, totalBytes);
    }
    else
    {
        for (int y = 0; y < m_textureHeight; ++y)
        {
            memcpy(dstData, srcData, srcPitch);
            srcData += srcPitch;
            dstData += lockedRect.Pitch;
        }
    }

    guard.texture->UnlockRect(0);
    guard.isLocked = false;

    hr = m_d3d9Device->UpdateTexture(guard.texture, m_d3d9Texture);
    if (FAILED(hr))
    {
        return false;
    }

    if (renderTextureUpdated)
    {
        onPlayEvent(ITPEventHandler::TPEventType::READY, EVENT_OK);
    }

    return true;
}

bool TP::TPPlayer::uploadTexturesD3D11WithGdiData(SharedSurfaceHandleInfo info) {
    // 在D3D11环境下处理GDI模式的共享内存数据
    if (!m_d3d11Device || !info.handle || info.width <= 0 || info.height <= 0)
    {
        return false;
    }

    bool renderTextureUpdated = false;

    // Create or update the RGBA texture
    if (!m_d3d11Texture || m_textureWidth != info.width || m_textureHeight != info.height) {
        m_textureWidth = info.width;
        m_textureHeight = info.height;

        D3D11_TEXTURE2D_DESC desc;
        ZeroMemory(&desc, sizeof(desc));
        desc.Width = m_textureWidth;
        desc.Height = m_textureHeight;
        desc.MipLevels = 1;
        desc.ArraySize = 1;
        desc.Format = DXGI_FORMAT_B8G8R8A8_UNORM;
        desc.SampleDesc.Count = 1;
        desc.Usage = D3D11_USAGE_DYNAMIC;
        desc.BindFlags = D3D11_BIND_SHADER_RESOURCE;
        desc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;

        if (m_d3d11Texture) {
            m_d3d11Texture->Release();
            m_d3d11Texture = nullptr;
        }

        renderTextureUpdated = true;
        HRESULT hr = m_d3d11Device->CreateTexture2D(&desc, nullptr, &m_d3d11Texture);
        if (FAILED(hr)) {
            return false;
        }
    }

    // Get device context
    if (m_d3d11DeviceContext == nullptr) {
        m_d3d11Device->GetImmediateContext(&m_d3d11DeviceContext);
    }

    // 处理共享内存数据 - info.handle是指向内存数据的指针
    const uint8_t* srcData = static_cast<const uint8_t*>(info.handle);
    if (!srcData)
    {
        return false;
    }

    // 使用D3D11的UpdateSubresource来更新纹理数据
    const int srcPitch = m_textureWidth * 4;  // BGRA格式，每像素4字节

    D3D11_MAPPED_SUBRESOURCE mappedResource;
    HRESULT hr = m_d3d11DeviceContext->Map(m_d3d11Texture, 0, D3D11_MAP_WRITE_DISCARD, 0, &mappedResource);

    if (SUCCEEDED(hr))
    {
        uint8_t* dstData = static_cast<uint8_t*>(mappedResource.pData);

        if (mappedResource.RowPitch == srcPitch)
        {
            // 如果行间距相同，可以一次性复制
            const int totalBytes = srcPitch * m_textureHeight;
            memcpy(dstData, srcData, totalBytes);
        }
        else
        {
            // 逐行复制
            for (int y = 0; y < m_textureHeight; ++y)
            {
                memcpy(dstData, srcData, srcPitch);
                srcData += srcPitch;
                dstData += mappedResource.RowPitch;
            }
        }

        m_d3d11DeviceContext->Unmap(m_d3d11Texture, 0);
    }
    else
    {
        // 如果Map失败，尝试使用UpdateSubresource
        m_d3d11DeviceContext->UpdateSubresource(
            m_d3d11Texture,
            0,
            nullptr,
            srcData,
            srcPitch,
            srcPitch * m_textureHeight
        );
    }

    if (renderTextureUpdated)
    {
        onPlayEvent(ITPEventHandler::TPEventType::READY, EVENT_OK);
    }

    return true;
}

void TP::TPPlayer::OnPrepared(::ITPPlayer* player) {
	onPlayEvent(ITPEventHandler::TPEventType::LOADED, EVENT_OK);
}

void TP::TPPlayer::OnCompletion(::ITPPlayer* player) {
	onPlayEvent(ITPEventHandler::TPEventType::COMPLETED, EVENT_OK);
}

char int64_buffer[50];
TPDownloadProgressInfo* progress_info;

void TP::TPPlayer::OnInfo(::ITPPlayer* player, TPOnInfoID on_info_id, TPOnInfoParam on_info_param) {
	switch (on_info_id) {
	case kTPOnInfoIDVoidFirstVideoFrameRendered:
		onPlayEvent(ITPEventHandler::TPEventType::FIRST_VIDEO_FRAME_RENDERED, EVENT_OK);
		onPlayEvent(ITPEventHandler::TPEventType::READY, EVENT_OK);
		break;
	case kTPOnInfoIDVoidBufferingStart:
		onPlayEvent(ITPEventHandler::TPEventType::BUFFER_START, EVENT_OK);
		break;
	case kTPOnInfoIDVoidBufferingEnd:
		onPlayEvent(ITPEventHandler::TPEventType::BUFFER_END, EVENT_OK);
		break;
	case kTPOnInfoIDObjDownloadProgressChanged:
		progress_info = (TPDownloadProgressInfo*)(on_info_param.obj_param);
		snprintf(int64_buffer, sizeof(int64_buffer), "%" PRIu64 ",%" PRIu64, progress_info->download_speed_bps, progress_info->available_position_ms);
		onPlayEvent(ITPEventHandler::TPEventType::DOWNLOAD_PROGRESS_CHANGED, int64_buffer);
		break;
	default:
		break;
	}
}

inline const char* TPGetErrorTypeName(TPPlayerErrorType value) {
	static constexpr std::pair<TPPlayerErrorType, const char*> kNameMap[] = {
		{kTPErrorTypeUnknown, "kTPErrorTypeUnknown"},
		{kTPErrorWindowsGeneral, "kTPErrorWindowsGeneral"},
		{kTPErrorWindowsCreateHandle, "kTPErrorWindowsCreateHandle"},
		{kTPErrorWindowsMappingFile, "kTPErrorWindowsMappingFile"},
		{kTPErrorTypeGeneral, "kTPErrorTypeGeneral"},
		{kTPErrorTypeDemuxerOthers, "kTPErrorTypeDemuxerOthers"},
		{kTPErrorTypeDemuxerNetwork, "kTPErrorTypeDemuxerNetwork"},
		{kTPErrorTypeDemuxerStream, "kTPErrorTypeDemuxerStream"},
		{kTPErrorTypeDemuxerBufferingTimeout, "kTPErrorTypeDemuxerBufferingTimeout"},
		{kTPErrorTypeDemuxerPrepareTimeout, "kTPErrorTypeDemuxerPrepareTimeout"},
		{kTPErrorTypeDecoderOthers, "kTPErrorTypeDecoderOthers"},
		{kTPErrorTypeDecoderAudioNotSupport, "kTPErrorTypeDecoderAudioNotSupport"},
		{kTPErrorTypeDecoderAudioStream, "kTPErrorTypeDecoderAudioStream"},
		{kTPErrorTypeDecoderVideoNotSupport, "kTPErrorTypeDecoderVideoNotSupport"},
		{kTPErrorTypeDecoderVideoStream, "kTPErrorTypeDecoderVideoStream"},
		{kTPErrorTypeDecoderSubtitleNotSupport, "kTPErrorTypeDecoderSubtitleNotSupport"},
		{kTPErrorTypeDecoderSubtitleStream, "kTPErrorTypeDecoderSubtitleStream"},
		{kTPErrorTypeRenderOthers, "kTPErrorTypeRenderOthers"},
		{kTPErrorTypeProcessAudioOthers, "kTPErrorTypeProcessAudioOthers"},
		{kTPErrorTypeProcessVideoOthers, "kTPErrorTypeProcessVideoOthers"},
		{kTPErrorTypeDownloadProxyGeneral, "kTPErrorTypeDownloadProxyGeneral"},
		{kTPErrorTypeDownloadProxyEnd, "kTPErrorTypeDownloadProxyEnd"},
	};
	const auto& it = std::find_if(std::begin(kNameMap), std::end(kNameMap),
		[value](auto p) { return value == p.first; });
	if (it == std::end(kNameMap)) {
		static std::string unknownError;
		std::ostringstream oss;
		oss << "error_type_" << static_cast<int>(value);
		unknownError = oss.str();
		return unknownError.c_str();
	}
	else {
		return it->second;
	}
}

void TP::TPPlayer::OnError(::ITPPlayer* player, ITPError* error) {
	std::ostringstream oss;
	oss << "on error: typeName:";
	oss << TPGetErrorTypeName(error->GetErrorType());
	oss << ", type: ";
	oss << error->GetErrorType();
	oss << ", code: ";
	oss << error->GetErrorCode();
	onPlayEvent((ITPEventHandler::TPEventType)3/*ITPEventHandler::TPEventType::ERROR*/, oss.str().c_str());
}

void TP::TPPlayer::OnVideoFrameOut(::ITPPlayer* player, ITPVideoFrameBuffer* video_frame_buffer) {
	updateFrame(video_frame_buffer);
}

TP::ITPPlayer* createWindowsPlayer(void* interfaces, bool forceGdiMode) {
	return new TP::TPPlayer(static_cast<IUnityInterfaces*>(interfaces), forceGdiMode);
}