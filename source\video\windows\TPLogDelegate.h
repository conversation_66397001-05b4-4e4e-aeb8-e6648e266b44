#pragma once

#include <ThumbPlayer/api/common/tp_log_callback_interface.h>

#include "ITPLog.h"

namespace TP {

	class TPLogDelegate : public ITPLogCallback {
	public:
		virtual ~TPLogDelegate();

		static TPLogDelegate& instance() {
			static TPLogDelegate logInstance;
			return logInstance;
		}

		void setLevel(int level) {
			_level = level;
		}

		void setDelegate(LogDelegate delegate) {
			_delegate = delegate;
		}

	protected:
		void LogWithLevel(TPLogLevel log_level, const char* tag, const char* content) override;

	private:
		int _level = 0;
		LogDelegate _delegate = nullptr;
	};
}
