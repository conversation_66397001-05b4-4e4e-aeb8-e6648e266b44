# D3D11环境下支持GDI模式功能说明

## 概述

本次修改为TPPlayer添加了在D3D11环境下强制使用GDI数据格式的功能。通过在CreatePlayer时传递参数，可以决定使用DXGI共享表面还是共享内存数据格式。

**重要说明**：GDI模式下仍然使用D3D11设备，只是数据传输方式从DXGI共享表面改为共享内存格式。

## 主要修改

### 1. TPPlayer类修改

#### 构造函数
- 原始：`TPPlayer(IUnityInterfaces* interfaces)`
- 新增：`TPPlayer(IUnityInterfaces* interfaces, bool forceGdiMode = false)`

#### 新增成员变量
- `bool m_forceGdiMode` - 控制是否强制使用GDI模式

### 2. 工厂函数

#### 原有函数（保持兼容性）
```cpp
TP::ITPPlayer* createWindowsPlayer(void* interfaces);
```

#### 新增函数
```cpp
TP::ITPPlayer* createWindowsPlayerWithGdiMode(void* interfaces, bool forceGdiMode);
```

### 3. 渲染器选择逻辑

修改前的逻辑：
- D3D9可用 → 使用kTPVideoRendererTypeGdi + 共享内存
- D3D11可用 → 使用kTPVideoRendererTypeD3d11 + DXGI共享表面

修改后的逻辑：
- D3D9可用 → 使用kTPVideoRendererTypeGdi + D3D9设备 + 共享内存
- forceGdiMode=true → 使用kTPVideoRendererTypeGdi + D3D11设备 + 共享内存
- 其他情况 → 使用kTPVideoRendererTypeD3d11 + D3D11设备 + DXGI共享表面

## 使用方法

### 方法1：默认模式（向后兼容）
```cpp
TP::ITPPlayer* player = TP::createWindowsPlayer(interfaces);
// 自动根据D3D设备类型选择渲染器
```

### 方法2：强制GDI模式
```cpp
TP::ITPPlayer* player = TP::createWindowsPlayerWithGdiMode(interfaces, true);
// 即使在D3D11环境下也使用GDI渲染模式
```

### 方法3：条件控制
```cpp
bool useGdiMode = /* 根据需求设置 */;
TP::ITPPlayer* player = TP::createWindowsPlayerWithGdiMode(interfaces, useGdiMode);
```

## 技术细节

### GDI模式特点
1. **渲染器类型**：kTPVideoRendererTypeGdi
2. **像素格式**：kTPPixelFormatSharedMemory
3. **数据传输**：通过共享内存传输视频帧数据（而非DXGI共享表面）
4. **设备使用**：
   - D3D9环境：使用D3D9设备处理共享内存数据
   - D3D11强制GDI模式：使用D3D11设备处理共享内存数据

### 内部实现
- `uploadTextures()` 方法根据设备类型和模式选择处理路径：
  - `m_isD3D9Mode` → `uploadTexturesD3D9()`
  - `m_forceGdiMode` → `uploadTexturesD3D11WithGdiData()`
  - 其他 → `uploadTexturesD3D11()`
- `getTexturePointer()` 方法返回相应的纹理指针（D3D9或D3D11）
- 像素格式和渲染器类型在构造函数中根据模式设置
- D3D11 GDI模式使用`D3D11_USAGE_DYNAMIC`纹理支持CPU写入

## 应用场景

1. **兼容性需求**：某些情况下需要在D3D11环境使用GDI渲染
2. **调试目的**：强制使用特定渲染模式进行问题排查
3. **性能测试**：比较不同渲染模式的性能差异
4. **特殊需求**：某些特定功能只在GDI模式下可用

## 注意事项

1. **向后兼容**：原有的`createWindowsPlayer`函数保持不变
2. **默认行为**：不传递参数时行为与之前完全一致
3. **资源管理**：GDI模式下仍使用D3D9相关的资源管理逻辑
4. **性能考虑**：GDI模式可能在某些情况下性能不如原生D3D11模式

## 测试建议

1. 在D3D11环境下测试强制GDI模式是否正常工作
2. 验证视频帧数据是否正确传输和渲染
3. 确认内存使用和性能表现
4. 测试与现有代码的兼容性
