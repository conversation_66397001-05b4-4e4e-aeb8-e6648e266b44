#pragma once

#include "TPLogDelegate.h"

#include "ITPLog.h"
#include "TPPlayer.h"

void TP::TPLogDelegate::LogWithLevel(TPLogLevel log_level, const char* tag, const char* content) {
	if (!_delegate) return;
	if (log_level < _level) return;
	_delegate(log_level, tag, content);
}

TP::TPLogDelegate::~TPLogDelegate() {
	TPGetPlayerMgr()->SetLogCallback(nullptr);
}

void initWindowsLogDelegate(int level, LogDelegate delegate) {
	TP::TPLogDelegate& impl = TP::TPLogDelegate::instance();
	impl.setLevel(level);
	impl.setDelegate(delegate);
	TPGetPlayerMgr()->SetLogCallback(&impl);
	TPGetPlayerMgr()->SetLogLevel((TPLogLevel)level);
}