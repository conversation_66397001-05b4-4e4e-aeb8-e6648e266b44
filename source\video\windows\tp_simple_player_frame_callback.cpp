/*****************************************************************************
 * @copyright Copyright (C), 1998-2023, Tencent Tech. Co., Ltd.
 * @file     tp_simple_player_frame_callback.cpp
 * @brief    数据帧回调示例
 * <AUTHOR>
 * @version  1.0.0
 * @date     2023/09/19
 * @license  GNU General Public License (GPL)
 *****************************************************************************/

#include "pch.h"

#include "demos/tp_simple_player_frame_callback.h"

#include "afxdialogex.h"
#include "common/tp_common.h"
#include "itp_data_transport_task_param.h"
#include "res/resource.h"
#include "tp_data_transport_enum.h"

static constexpr char *kUrl =
    "https://**********.vod-qcloud.com/f80e0663vodtranssgp**********/249871973560136623126552730/v.f101280.m3u8";
    // "http://playertest-75538.gzc.vod.tencent-cloud.com/hls/"
    // "hls_definition_source/hls_h264_480_270.m3u8";

// TPSimplePlayerFrameCallback 对话框
IMPLEMENT_DYNAMIC(TPSimplePlayerFrameCallback, CDialogEx)

BEGIN_MESSAGE_MAP(TPSimplePlayerFrameCallback, CDialogEx)
ON_WM_CLOSE()
ON_BN_CLICKED(IDC_CHECK_VIDEO_CB, &TPSimplePlayerFrameCallback::OnClickedVideoCb)
ON_BN_CLICKED(IDC_CHECK_AUDIO_CB, &TPSimplePlayerFrameCallback::OnClickedAudioCb)
END_MESSAGE_MAP()

void TPSimplePlayerFrameCallback::DoDataExchange(CDataExchange *pDX) {
  CDialogEx::DoDataExchange(pDX);
  DDX_Control(pDX, IDC_STATIC_PLAY_VIEW, preview_window_);
  DDX_Control(pDX, IDC_CHECK_VIDEO_CB, check_video_frame_callback_);
  DDX_Control(pDX, IDC_CHECK_AUDIO_CB, check_audio_frame_callback_);
  DDX_Control(pDX, IDC_STATIC_VIDEO_FRAME_COUNT, text_video_frame_count_);
  DDX_Control(pDX, IDC_STATIC_AUDIO_FRAME_COUNT, text_audio_frame_count_);
}

TPSimplePlayerFrameCallback::TPSimplePlayerFrameCallback(CWnd *pParent)
    : CDialogEx(IDD_DIALOG_PLAY_FRAME_CB, pParent) {
}

TPSimplePlayerFrameCallback::~TPSimplePlayerFrameCallback() {
  CleanupD3D9();
}

BOOL TPSimplePlayerFrameCallback::OnInitDialog() {
  CDialogEx::OnInitDialog();

  // 初始化 D3D9
  if (!InitD3D9()) {
    MessageBox(L"Failed to initialize D3D9", L"Error", MB_OK);
    return FALSE;
  }

  CreatePlayerAndPlay();
  return TRUE;
}

void TPSimplePlayerFrameCallback::CreatePlayerAndPlay() {
  player_ = TPCreatePlayer();
  // 无窗渲染不需要给播放器设置窗口
  player_->SetWindow(nullptr);

  // 启用视频帧回调
  auto param = BuildBoolParam(kTPOptionalIDGlobalBoolEnableVideoFrameCallback, true);
  player_->AddOptionalParam(param);
  param->Destroy();

  // 设置内容类型
  auto param_content =
      BuildIntParam(kTPOptionalIDBeforeIntOutVideoContentMode, kTPVideoCallbackContentModeAll);
  player_->AddOptionalParam(param_content);
  param_content->Destroy();

  // 设置跨进程像素格式为共享内存（GDI渲染）
  auto param_format =
      BuildIntParam(kTPOptionalIDBeforeIntOutVideoPixelFormat, kTPPixelFormatSharedMemory);
  player_->AddOptionalParam(param_format);
  param_format->Destroy();

  // 设置渲染器类型为 GDI，这样会产生共享内存格式的视频帧
  ITPArray<int> *renders = CreateIntTPArray();
  renders->AddValue(kTPVideoRendererTypeGdi);
  auto param_render = BuildQueueIntParam(kTPOptionalIDBeforeQueueIntVideoRendererType, renders);
  player_->AddOptionalParam(param_render);
  param_render->Destroy();
  renders->Destroy();

  player_->SetOnPreparedCallback(this);
  player_->SetOnVideoFrameOutCallback(this);
  player_->SetOnAudioFrameOutCallback(this);
  SetDataSource();
  player_->PrepareAsync();
}

void TPSimplePlayerFrameCallback::SetDataSource() {
  // 获取media asset
  ITPUrlMediaAsset *asset = TPCreateUrlMediaAsset();
  asset->SetUrl(kUrl);
  asset->SetParam(tpdlproxy::kTaskParamIntFileType,
                  std::to_string(tpdlproxy::kTPTaskFileTypeHlsLive).c_str());
  player_->SetDataSource(asset);
  asset->Destroy();
}

void TPSimplePlayerFrameCallback::OnPrepared(ITPPlayer *player) {
  RECT rect;
  ::GetClientRect(preview_window_.m_hWnd, &rect);
  player_->GetPresenterInterface()->NotifyWindowsResize(rect);
  player_->Start();
}

void TPSimplePlayerFrameCallback::OnClose() {
  CDialogEx::OnClose();
  DestroyPlayer();
}

void TPSimplePlayerFrameCallback::DestroyPlayer() {
  if (nullptr == player_) {
    return;
  }
  player_->SetOnPreparedCallback(nullptr);
  player_->Stop();
  player_->Reset();
  player_->Release();
  player_->Destroy();
  player_ = nullptr;
}

////// simple player基础上新增的内容
void TPSimplePlayerFrameCallback::OnClickedVideoCb() {
  bool enable_callback = check_video_frame_callback_.GetCheck() == 1;
  auto param = BuildBoolParam(kTPOptionalIDGlobalBoolEnableVideoFrameCallback, enable_callback);
  player_->AddOptionalParam(param);
  param->Destroy();
}

void TPSimplePlayerFrameCallback::OnClickedAudioCb() {
  bool enable_callback = check_audio_frame_callback_.GetCheck() == 1;
  auto param = BuildBoolParam(kTPOptionalIDGlobalBoolEnableAudioFrameCallback, enable_callback);
  player_->AddOptionalParam(param);
  param->Destroy();
}

void TPSimplePlayerFrameCallback::OnVideoFrameOut(ITPPlayer *player,
                                                ITPVideoFrameBuffer *video_frame_buffer) {
  video_frame_count_++;
  text_video_frame_count_.SetWindowTextW(std::to_wstring(video_frame_count_).c_str());

  // 手动渲染视频帧到 preview_window_
  if (video_frame_buffer) {
    TPPixelFormat format = video_frame_buffer->GetFormat();
    int width = video_frame_buffer->GetWidth();
    int height = video_frame_buffer->GetHeight();
    HANDLE handle = reinterpret_cast<HANDLE>(video_frame_buffer->GetData()[0]);

    // 根据像素格式进行不同的渲染处理
    if (format == kTPPixelFormatSharedMemory) {
      // 共享内存格式，转换为 D3D9 纹理渲染
      RenderD3D9Frame(width, height, handle);
    }
    // 其他格式暂时不处理，可以根据需要扩展
  }
}

void TPSimplePlayerFrameCallback::OnAudioFrameOut(ITPPlayer *player,
                                                ITPAudioFrameBuffer *audio_frame_buffer) {
  audio_frame_count_++;
  text_audio_frame_count_.SetWindowTextW(std::to_wstring(audio_frame_count_).c_str());
}

bool TPSimplePlayerFrameCallback::RenderGDIFrame(int width, int height, HANDLE handle) {
  // handle 实际上是共享内存的名称（wchar_t*）
  wchar_t* name = reinterpret_cast<wchar_t*>(handle);
  int len = width * height * 4;  // BGRA 格式，每像素 4 字节

  // 打开共享内存
  HANDLE file_handle = ::CreateFileMapping(INVALID_HANDLE_VALUE, nullptr, PAGE_READWRITE, 0, len, name);
  if (INVALID_HANDLE_VALUE == file_handle) {
    return false;
  }

  // 映射共享内存
  void* data = ::MapViewOfFile(file_handle, FILE_MAP_READ, 0, 0, len);
  if (!data) {
    CloseHandle(file_handle);
    return false;
  }

  // 获取目标窗口的设备上下文
  HDC hdc = ::GetDC(preview_window_.m_hWnd);
  HDC compatible_dc = CreateCompatibleDC(hdc);
  SetStretchBltMode(compatible_dc, COLORONCOLOR);

  // 设置位图信息
  BITMAPINFOHEADER bmi{sizeof(BITMAPINFOHEADER)};
  bmi.biWidth = width;
  bmi.biHeight = -(static_cast<LONG>(height));  // 负值表示自上而下的位图
  bmi.biSizeImage = width * height * 4;
  bmi.biBitCount = 32;
  bmi.biCompression = BI_RGB;
  bmi.biPlanes = 1;

  // 创建 DIB 位图
  HBITMAP bmp = CreateDIBSection(compatible_dc, reinterpret_cast<BITMAPINFO*>(&bmi),
                                 DIB_RGB_COLORS, nullptr, file_handle, 0);
  if (bmp) {
    // 选择位图到设备上下文
    HGDIOBJ old_bmp = SelectObject(compatible_dc, bmp);

    // 获取目标窗口大小
    RECT rect;
    ::GetClientRect(preview_window_.m_hWnd, &rect);

    // 将位图绘制到目标窗口，支持缩放
    StretchBlt(hdc, 0, 0, rect.right, rect.bottom,
               compatible_dc, 0, 0, width, height, SRCCOPY);

    // 清理资源
    SelectObject(compatible_dc, old_bmp);
    DeleteObject(bmp);
  }

  DeleteDC(compatible_dc);
  ::ReleaseDC(preview_window_.m_hWnd, hdc);
  ::UnmapViewOfFile(data);
  CloseHandle(file_handle);

  return true;
}

bool TPSimplePlayerFrameCallback::InitD3D9() {
  // 创建 D3D9 对象
  d3d9_ = Direct3DCreate9(D3D_SDK_VERSION);
  if (!d3d9_) {
    return false;
  }

  // 设置设备参数
  D3DPRESENT_PARAMETERS d3dpp;
  ZeroMemory(&d3dpp, sizeof(d3dpp));
  d3dpp.Windowed = TRUE;
  d3dpp.SwapEffect = D3DSWAPEFFECT_DISCARD;
  d3dpp.BackBufferFormat = D3DFMT_UNKNOWN;
  d3dpp.hDeviceWindow = preview_window_.m_hWnd;
  d3dpp.EnableAutoDepthStencil = FALSE;
  d3dpp.PresentationInterval = D3DPRESENT_INTERVAL_IMMEDIATE;

  // 创建 D3D9 设备
  HRESULT hr = d3d9_->CreateDevice(
      D3DADAPTER_DEFAULT,
      D3DDEVTYPE_HAL,
      preview_window_.m_hWnd,
      D3DCREATE_SOFTWARE_VERTEXPROCESSING,
      &d3dpp,
      &d3d_device_);

  if (FAILED(hr)) {
    CleanupD3D9();
    return false;
  }

  return true;
}

void TPSimplePlayerFrameCallback::CleanupD3D9() {
  if (video_texture_) {
    video_texture_->Release();
    video_texture_ = nullptr;
  }
  if (d3d_device_) {
    d3d_device_->Release();
    d3d_device_ = nullptr;
  }
  if (d3d9_) {
    d3d9_->Release();
    d3d9_ = nullptr;
  }
  texture_width_ = 0;
  texture_height_ = 0;
}

bool TPSimplePlayerFrameCallback::CreateVideoTexture(int width, int height) {
  // 如果纹理尺寸没有变化，不需要重新创建
  if (video_texture_ && texture_width_ == width && texture_height_ == height) {
    return true;
  }

  // 释放旧纹理
  if (video_texture_) {
    video_texture_->Release();
    video_texture_ = nullptr;
  }

  // 创建新纹理
  HRESULT hr = d3d_device_->CreateTexture(
      width, height, 1, 0,
      D3DFMT_A8R8G8B8,  // BGRA 格式
      D3DPOOL_MANAGED,
      &video_texture_,
      nullptr);

  if (FAILED(hr)) {
    return false;
  }

  texture_width_ = width;
  texture_height_ = height;
  return true;
}

bool TPSimplePlayerFrameCallback::RenderD3D9Frame(int width, int height, HANDLE handle) {
  if (!d3d_device_) {
    return false;
  }

  // handle 实际上是共享内存的名称（wchar_t*）
  wchar_t* name = reinterpret_cast<wchar_t*>(handle);
  int len = width * height * 4;  // BGRA 格式，每像素 4 字节

  // 打开共享内存
  HANDLE file_handle = ::CreateFileMapping(INVALID_HANDLE_VALUE, nullptr, PAGE_READWRITE, 0, len, name);
  if (INVALID_HANDLE_VALUE == file_handle) {
    return false;
  }

  // 映射共享内存
  void* data = ::MapViewOfFile(file_handle, FILE_MAP_READ, 0, 0, len);
  if (!data) {
    CloseHandle(file_handle);
    return false;
  }

  // 创建或更新纹理
  bool success = CreateVideoTexture(width, height) &&
                 UpdateTextureFromMemory(width, height, data);

  if (success) {
    // 渲染纹理到窗口
    RenderTextureToWindow();
  }

  // 清理资源
  ::UnmapViewOfFile(data);
  CloseHandle(file_handle);

  return success;
}

bool TPSimplePlayerFrameCallback::UpdateTextureFromMemory(int width, int height, void* data) {
  if (!video_texture_) {
    return false;
  }

  // 锁定纹理
  D3DLOCKED_RECT locked_rect;
  HRESULT hr = video_texture_->LockRect(0, &locked_rect, nullptr, 0);
  if (FAILED(hr)) {
    return false;
  }

  // 复制数据到纹理
  uint8_t* src = static_cast<uint8_t*>(data);
  uint8_t* dst = static_cast<uint8_t*>(locked_rect.pBits);
  int src_pitch = width * 4;  // BGRA 格式，每像素 4 字节

  for (int y = 0; y < height; ++y) {
    memcpy(dst, src, src_pitch);
    src += src_pitch;
    dst += locked_rect.Pitch;
  }

  // 解锁纹理
  video_texture_->UnlockRect(0);
  return true;
}

void TPSimplePlayerFrameCallback::RenderTextureToWindow() {
  if (!d3d_device_ || !video_texture_) {
    return;
  }

  // 获取窗口大小
  RECT rect;
  ::GetClientRect(preview_window_.m_hWnd, &rect);
  int window_width = rect.right;
  int window_height = rect.bottom;

  // 开始渲染
  if (SUCCEEDED(d3d_device_->BeginScene())) {
    // 清除背景
    d3d_device_->Clear(0, nullptr, D3DCLEAR_TARGET, D3DCOLOR_XRGB(0, 0, 0), 1.0f, 0);

    // 设置渲染状态
    d3d_device_->SetRenderState(D3DRS_LIGHTING, FALSE);
    d3d_device_->SetRenderState(D3DRS_CULLMODE, D3DCULL_NONE);

    // 设置纹理
    d3d_device_->SetTexture(0, video_texture_);
    d3d_device_->SetTextureStageState(0, D3DTSS_COLOROP, D3DTOP_SELECTARG1);
    d3d_device_->SetTextureStageState(0, D3DTSS_COLORARG1, D3DTA_TEXTURE);
    d3d_device_->SetTextureStageState(0, D3DTSS_ALPHAOP, D3DTOP_DISABLE);

    // 设置采样器状态
    d3d_device_->SetSamplerState(0, D3DSAMP_MINFILTER, D3DTEXF_LINEAR);
    d3d_device_->SetSamplerState(0, D3DSAMP_MAGFILTER, D3DTEXF_LINEAR);

    // 创建顶点数据（全屏四边形）
    struct Vertex {
      float x, y, z, rhw;
      float u, v;
    };

    Vertex vertices[4] = {
      {0.0f,                    0.0f,                     0.0f, 1.0f, 0.0f, 0.0f},  // 左上
      {(float)window_width,     0.0f,                     0.0f, 1.0f, 1.0f, 0.0f},  // 右上
      {0.0f,                    (float)window_height,     0.0f, 1.0f, 0.0f, 1.0f},  // 左下
      {(float)window_width,     (float)window_height,     0.0f, 1.0f, 1.0f, 1.0f}   // 右下
    };

    // 设置 FVF 格式
    d3d_device_->SetFVF(D3DFVF_XYZRHW | D3DFVF_TEX1);

    // 绘制四边形
    d3d_device_->DrawPrimitiveUP(D3DPT_TRIANGLESTRIP, 2, vertices, sizeof(Vertex));

    // 结束渲染
    d3d_device_->EndScene();
  }

  // 呈现到屏幕
  d3d_device_->Present(nullptr, nullptr, nullptr, nullptr);
}